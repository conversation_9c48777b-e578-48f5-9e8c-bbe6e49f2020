# 远程服务器配置文件更新工具

这个工具用于通过SSH连接远程服务器，并更新cdata2、ckernel2、cproxy2三个服务的配置文件。

## 功能特性

- 通过SSH连接远程服务器
- 自动备份远程配置文件（备份名格式：原文件名+时间戳）
- 使用场景ID从Excel文件读取配置
- 支持密码和密钥两种SSH认证方式
- 使用自定义日志系统记录操作

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置说明

### 1. 修改连接参数

在 `remote_config_updater.py` 文件的 `main()` 函数中修改以下参数：

```python
HOSTNAME = "your_server_ip"  # 远程服务器IP
USERNAME = "your_username"   # SSH用户名
PASSWORD = "your_password"   # SSH密码（如果使用密码认证）
KEY_FILENAME = None          # SSH私钥文件路径（如果使用密钥认证）
PORT = 22                    # SSH端口
```

### 2. 配置本地config.ini文件

编辑 `config.ini` 文件，设置场景ID：

```ini
[DEFAULT]
scene_id = 1
```

### 3. 配置Excel配置文件

使用 `connfig.xlsx` 文件，包含以下列结构：

| 列 | 字段名 | 服务 | 说明 |
|---|---|---|---|
| 1 | 场景id | - | 场景ID（阶梯1、阶梯2、阶梯3） |
| 2 | MDBQryThreadNum | ckernel2 | 数据库查询线程数 |
| 3 | genfiletheadnum | ckernel2 | 文件生成线程数 |
| 4 | exportThreadNum | ckernel2 | 导出线程数 |
| 5 | ImportThreadNum | ckernel2 | 导入线程数 |
| 6 | exportThreadNum | cdata2 | 导出线程数 |
| 7 | FlowProThreadNum | cdata2 | 流程处理线程数 |
| 8 | PgProxyThreadNum | cproxy2 | 代理线程数 |

Excel文件格式说明：
- 第一行：字段标题
- 第二行开始：具体配置数据
- 场景ID使用中文：阶梯1、阶梯2、阶梯3

### 4. 导入自定义日志系统

在 `remote_config_updater.py` 文件顶部取消注释并修改日志模块导入：

```python
# 请根据实际的日志模块路径进行导入
from your_log_module import log_operation
```

## 使用方法

```bash
python remote_config_updater.py
```

## 操作流程

1. 连接到远程服务器
2. 读取本地config.ini获取场景ID
3. 根据场景ID从Excel文件读取对应配置
4. 对每个服务执行以下操作：
   - 备份远程配置文件
   - 读取远程配置文件
   - 更新配置字段
   - 上传更新后的配置文件
5. 输出更新结果
6. 断开SSH连接

## 日志记录

工具使用自定义日志系统记录以下操作：

- `SSH_CONNECT`: SSH连接操作
- `SSH_DISCONNECT`: SSH断开连接操作
- `FILE_BACKUP`: 文件备份操作
- `READ_LOCAL_CONFIG`: 读取本地配置操作
- `READ_EXCEL_CONFIG`: 读取Excel配置操作
- `READ_REMOTE_CONFIG`: 读取远程配置操作
- `UPDATE_CONFIG`: 更新配置操作

## 注意事项

1. 确保远程服务器上的文件路径正确
2. 确保有足够的权限访问和修改远程文件
3. 备份文件会保存在原文件同目录下
4. 如果远程配置文件中没有对应的配置节，会自动创建
5. Excel文件的第一列必须是场景ID
6. 确保Excel文件中的列数与配置字段数量匹配

## 错误处理

- 连接失败时会记录错误日志
- 文件不存在时会记录警告日志
- 配置更新失败时会记录错误日志
- 所有操作都有相应的成功/失败状态记录
