#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
远程服务器配置文件更新工具
用于通过SSH连接远程服务器并更新cdata2、ckernel2、cproxy2三个服务的配置文件
"""

import paramiko
import configparser
import os
import datetime
import pandas as pd
from typing import Dict, List, Tuple

# 导入自定义日志系统
# 请根据实际的日志模块路径进行导入
# from your_log_module import log_operation

class RemoteConfigUpdater:
    def __init__(self, hostname: str, username: str, password: str = None, key_filename: str = None, port: int = 22):
        """
        初始化远程配置更新器
        
        Args:
            hostname: 远程服务器地址
            username: SSH用户名
            password: SSH密码（如果使用密码认证）
            key_filename: SSH私钥文件路径（如果使用密钥认证）
            port: SSH端口，默认22
        """
        self.hostname = hostname
        self.username = username
        self.password = password
        self.key_filename = key_filename
        self.port = port
        self.ssh_client = None
        
        # 服务配置映射
        self.service_configs = {
            'cdata2': {
                'remote_path': 'cdata2/bin/cdata.ini',
                'config_fields': ['exportThreadNum', 'FlowProThreadNum'],
                'excel_columns': [5, 6]  # 第6-7列 (索引5,6)
            },
            'ckernel2': {
                'remote_path': 'ckernel2/bin/ckernel.ini',
                'config_fields': ['MDBQryThreadNum', 'genfiletheadnum', 'exportThreadNum', 'ImportThreadNum'],
                'excel_columns': [1, 2, 3, 4]  # 第2-5列 (索引1,2,3,4)
            },
            'cproxy2': {
                'remote_path': 'cproxy2/bin/cproxy.ini',
                'config_fields': ['PgProxyThreadNum'],
                'excel_columns': [7]  # 第8列 (索引7)
            }
        }
    
    def connect(self) -> bool:
        """建立SSH连接"""
        try:
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            if self.key_filename:
                self.ssh_client.connect(
                    hostname=self.hostname,
                    username=self.username,
                    key_filename=self.key_filename,
                    port=self.port
                )
            else:
                self.ssh_client.connect(
                    hostname=self.hostname,
                    username=self.username,
                    password=self.password,
                    port=self.port
                )
            
            log_operation(
                operation_type='SSH_CONNECT',
                business_type='REMOTE_CONFIG',
                is_success=True
            )
            print(f"成功连接到远程服务器: {self.hostname}")
            return True
            
        except Exception as e:
            log_operation(
                operation_type='SSH_CONNECT',
                business_type='REMOTE_CONFIG',
                is_success=False
            )
            print(f"连接远程服务器失败: {e}")
            return False
    
    def disconnect(self):
        """断开SSH连接"""
        if self.ssh_client:
            self.ssh_client.close()
            log_operation(
                operation_type='SSH_DISCONNECT',
                business_type='REMOTE_CONFIG',
                is_success=True
            )
            print("已断开SSH连接")
    
    def backup_remote_file(self, remote_path: str) -> str:
        """
        备份远程文件
        
        Args:
            remote_path: 远程文件路径
            
        Returns:
            备份文件路径
        """
        try:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{remote_path}.backup_{timestamp}"
            
            # 执行备份命令
            command = f"cp {remote_path} {backup_path}"
            stdin, stdout, stderr = self.ssh_client.exec_command(command)
            
            exit_status = stdout.channel.recv_exit_status()
            if exit_status == 0:
                log_operation(
                    operation_type='FILE_BACKUP',
                    business_type='REMOTE_CONFIG',
                    is_success=True
                )
                print(f"成功备份文件: {remote_path} -> {backup_path}")
                return backup_path
            else:
                error_msg = stderr.read().decode('utf-8')
                log_operation(
                    operation_type='FILE_BACKUP',
                    business_type='REMOTE_CONFIG',
                    is_success=False
                )
                print(f"备份文件失败: {error_msg}")
                return None
                
        except Exception as e:
            log_operation(
                operation_type='FILE_BACKUP',
                business_type='REMOTE_CONFIG',
                is_success=False
            )
            print(f"备份文件时发生错误: {e}")
            return None
    
    def read_local_config(self, config_file: str = 'config.ini') -> Dict:
        """
        读取本地配置文件，获取场景ID
        
        Args:
            config_file: 本地配置文件路径
            
        Returns:
            包含场景ID的字典
        """
        try:
            if not os.path.exists(config_file):
                log_operation(
                    operation_type='READ_LOCAL_CONFIG',
                    business_type='REMOTE_CONFIG',
                    is_success=False
                )
                print(f"本地配置文件不存在: {config_file}")
                return None
            
            config = configparser.ConfigParser()
            config.read(config_file, encoding='utf-8')
            
            if 'DEFAULT' not in config or 'scene_id' not in config['DEFAULT']:
                log_operation(
                    operation_type='READ_LOCAL_CONFIG',
                    business_type='REMOTE_CONFIG',
                    is_success=False
                )
                print("本地配置文件中没有找到场景ID配置")
                return None
            
            scene_id = config['DEFAULT']['scene_id']
            log_operation(
                operation_type='READ_LOCAL_CONFIG',
                business_type='REMOTE_CONFIG',
                is_success=True
            )
            print(f"成功读取场景ID: {scene_id}")
            return {'scene_id': scene_id}
            
        except Exception as e:
            log_operation(
                operation_type='READ_LOCAL_CONFIG',
                business_type='REMOTE_CONFIG',
                is_success=False
            )
            print(f"读取本地配置文件失败: {e}")
            return None
    
    def read_excel_config(self, excel_file: str = 'connfig.xlsx', scene_id: str = None) -> Dict:
        """
        从Excel文件读取配置
        
        Args:
            excel_file: Excel文件路径
            scene_id: 场景ID
            
        Returns:
            配置字典
        """
        try:
            if not os.path.exists(excel_file):
                log_operation(
                    operation_type='READ_EXCEL_CONFIG',
                    business_type='REMOTE_CONFIG',
                    is_success=False
                )
                print(f"Excel配置文件不存在: {excel_file}")
                return None
            
            # 读取Excel文件，跳过第一行标题
            df = pd.read_excel(excel_file, skiprows=1)
            
            # 查找场景ID对应的行
            scene_row = None
            for index, row in df.iterrows():
                if str(row.iloc[0]) == str(scene_id):  # 第一列是场景ID
                    scene_row = row
                    break
            
            if scene_row is None:
                log_operation(
                    operation_type='READ_EXCEL_CONFIG',
                    business_type='REMOTE_CONFIG',
                    is_success=False
                )
                print(f"在Excel文件中没有找到场景ID: {scene_id}")
                return None
            
            # 构建配置字典
            config = {}
            for service_name, service_info in self.service_configs.items():
                config[service_name] = {}
                excel_columns = service_info['excel_columns']
                config_fields = service_info['config_fields']
                
                for i, field in enumerate(config_fields):
                    if i < len(excel_columns):
                        col_index = excel_columns[i]
                        if col_index < len(scene_row):
                            config[service_name][field] = str(scene_row.iloc[col_index])
            
            log_operation(
                operation_type='READ_EXCEL_CONFIG',
                business_type='REMOTE_CONFIG',
                is_success=True
            )
            print(f"成功从Excel读取场景 {scene_id} 的配置")
            return config
            
        except Exception as e:
            log_operation(
                operation_type='READ_EXCEL_CONFIG',
                business_type='REMOTE_CONFIG',
                is_success=False
            )
            print(f"读取Excel配置文件失败: {e}")
            return None
    
    def read_remote_config(self, remote_path: str) -> configparser.ConfigParser:
        """
        读取远程配置文件
        
        Args:
            remote_path: 远程文件路径
            
        Returns:
            配置解析器对象
        """
        try:
            # 读取远程文件内容
            command = f"cat {remote_path}"
            stdin, stdout, stderr = self.ssh_client.exec_command(command)
            
            exit_status = stdout.channel.recv_exit_status()
            if exit_status != 0:
                error_msg = stderr.read().decode('utf-8')
                log_operation(
                    operation_type='READ_REMOTE_CONFIG',
                    business_type='REMOTE_CONFIG',
                    is_success=False
                )
                print(f"读取远程文件失败: {error_msg}")
                return None
            
            content = stdout.read().decode('utf-8')
            
            # 解析配置
            config = configparser.ConfigParser()
            config.read_string(content)
            log_operation(
                operation_type='READ_REMOTE_CONFIG',
                business_type='REMOTE_CONFIG',
                is_success=True
            )
            print(f"成功读取远程配置文件: {remote_path}")
            return config
            
        except Exception as e:
            log_operation(
                operation_type='READ_REMOTE_CONFIG',
                business_type='REMOTE_CONFIG',
                is_success=False
            )
            print(f"读取远程配置文件失败: {e}")
            return None
    
    def update_remote_config(self, service_name: str, excel_config: Dict) -> bool:
        """
        更新远程服务配置
        
        Args:
            service_name: 服务名称
            excel_config: Excel配置字典
            
        Returns:
            是否更新成功
        """
        try:
            service_info = self.service_configs.get(service_name)
            if not service_info:
                log_operation(
                    operation_type='UPDATE_CONFIG',
                    business_type='REMOTE_CONFIG',
                    is_success=False
                )
                print(f"未知的服务名称: {service_name}")
                return False
            
            remote_path = service_info['remote_path']
            config_fields = service_info['config_fields']
            
            # 备份远程文件
            backup_path = self.backup_remote_file(remote_path)
            if not backup_path:
                return False
            
            # 读取远程配置
            remote_config = self.read_remote_config(remote_path)
            if not remote_config:
                return False
            
            # 检查Excel配置中是否有对应服务的配置
            if service_name not in excel_config:
                log_operation(
                    operation_type='UPDATE_CONFIG',
                    business_type='REMOTE_CONFIG',
                    is_success=False
                )
                print(f"Excel配置中没有找到 {service_name} 的配置")
                return False
            
            # 更新配置字段
            updated = False
            for field in config_fields:
                if field in excel_config[service_name]:
                    new_value = excel_config[service_name][field]
                    if service_name in remote_config.sections():
                        remote_config[service_name][field] = new_value
                        print(f"更新 {service_name} 的 {field}: {new_value}")
                        updated = True
                    else:
                        # 如果远程配置中没有对应的节，创建一个
                        remote_config[service_name] = {}
                        remote_config[service_name][field] = new_value
                        print(f"创建 {service_name} 节并设置 {field}: {new_value}")
                        updated = True
            
            if not updated:
                log_operation(
                    operation_type='UPDATE_CONFIG',
                    business_type='REMOTE_CONFIG',
                    is_success=False
                )
                print(f"没有找到需要更新的配置字段")
                return False
            
            # 将更新后的配置写回远程文件
            config_content = ""
            for section in remote_config.sections():
                config_content += f"[{section}]\n"
                for key, value in remote_config[section].items():
                    config_content += f"{key} = {value}\n"
                config_content += "\n"
            
            # 创建临时文件并上传
            temp_file = f"/tmp/{service_name}_config_temp.ini"
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(config_content)
            
            # 上传文件到远程服务器
            sftp = self.ssh_client.open_sftp()
            sftp.put(temp_file, remote_path)
            sftp.close()
            
            # 删除临时文件
            os.remove(temp_file)
            
            log_operation(
                operation_type='UPDATE_CONFIG',
                business_type='REMOTE_CONFIG',
                is_success=True
            )
            print(f"成功更新 {service_name} 的配置文件")
            return True
            
        except Exception as e:
            log_operation(
                operation_type='UPDATE_CONFIG',
                business_type='REMOTE_CONFIG',
                is_success=False
            )
            print(f"更新 {service_name} 配置时发生错误: {e}")
            return False
    
    def update_all_services(self, config_file: str = 'config.ini', excel_file: str = 'config.xlsx') -> Dict[str, bool]:
        """
        更新所有服务的配置
        
        Args:
            config_file: 本地配置文件路径
            excel_file: Excel配置文件路径
            
        Returns:
            各服务更新结果字典
        """
        results = {}
        
        # 读取本地配置获取场景ID
        local_config = self.read_local_config(config_file)
        if not local_config:
            return results
        
        scene_id = local_config['scene_id']
        
        # 从Excel读取配置
        excel_config = self.read_excel_config(excel_file, scene_id)
        if not excel_config:
            return results
        
        # 更新每个服务
        for service_name in self.service_configs.keys():
            print(f"开始更新 {service_name} 服务配置...")
            results[service_name] = self.update_remote_config(service_name, excel_config)
        
        return results

def main():
    """主函数"""
    # 配置参数 - 请根据实际情况修改
    HOSTNAME = "your_server_ip"  # 远程服务器IP
    USERNAME = "your_username"   # SSH用户名
    PASSWORD = "your_password"   # SSH密码（如果使用密码认证）
    KEY_FILENAME = None          # SSH私钥文件路径（如果使用密钥认证）
    PORT = 22                    # SSH端口
    
    # 创建更新器实例
    updater = RemoteConfigUpdater(
        hostname=HOSTNAME,
        username=USERNAME,
        password=PASSWORD,
        key_filename=KEY_FILENAME,
        port=PORT
    )
    
    try:
        # 连接远程服务器
        if not updater.connect():
            print("无法连接到远程服务器，程序退出")
            return
        
        # 更新所有服务配置
        results = updater.update_all_services('config.ini', 'connfig.xlsx')
        
        # 输出结果
        print("配置更新完成，结果如下:")
        for service, success in results.items():
            status = "成功" if success else "失败"
            print(f"{service}: {status}")
        
    except KeyboardInterrupt:
        print("用户中断操作")
    except Exception as e:
        print(f"程序执行时发生错误: {e}")
    finally:
        # 断开连接
        updater.disconnect()

if __name__ == "__main__":
    main()
